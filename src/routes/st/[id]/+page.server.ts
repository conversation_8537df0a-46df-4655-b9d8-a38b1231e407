import { getById } from '$lib/db';
import type { School, SchoolUser } from '$lib/types';
import { error } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params }) => {
  const student = await getById<SchoolUser>(params.id);
  if (!student) {
    throw error(404, "student not found");
  }
  const school = await getById<School>(student.sc);
  if (!school) {
    throw error(404, "student's school not found");
  }
  if (!school.c) {
    throw error(404, "student's school classes not found");
  }
  return { student: { ...student, cl: school.c[student.c] } };
}; 
<script lang="ts">
  import type { PageData } from './$types';
  export let data: PageData;
</script>

<div class="max-w-xl mx-auto py-12">
  <h1 class="text-3xl font-bold mb-4">Student Details</h1>
    <div class="mb-6">
      <div class="text-xl font-semibold">{data.student.n}</div>
      <div class="text-gray-700">{data.student.cl}</div>
    </div>
    <a href={`/s/${data.student.id}/${data.student.c}/3`} class="inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Go to Midterm Page</a>
</div> 
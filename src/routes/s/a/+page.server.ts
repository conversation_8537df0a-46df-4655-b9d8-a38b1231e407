import { fail, redirect } from '@sveltejs/kit';
import { upsertPoint, getById, searchByPayload } from '$lib/qdrant';
import type { Actions, PageServerLoad } from './$types';
import type { Score, SchoolUser } from '$lib/types';
import { requireAuth } from '$lib/auth.server';

export const load: PageServerLoad = async (event) => {
  await requireAuth(event.locals);
  return {};
};

export const actions = {
  submit: async ({ request, locals }) => {
    const data = await request.formData();
    const studentId = data.get('studentId')?.toString();
    const term = parseInt(data.get('term')?.toString() || '');
    const yearKey = data.get('yearKey')?.toString();
    const academicYear = parseInt(data.get('academicYear')?.toString() || '');
    const subject = data.get('subject')?.toString();
    
    // Optional scores
    const ca1 = parseInt(data.get('ca1')?.toString() || '') || undefined;
    const ca2 = parseInt(data.get('ca2')?.toString() || '') || undefined;
    const ca3 = parseInt(data.get('ca3')?.toString() || '') || undefined;
    const project = parseInt(data.get('project')?.toString() || '') || undefined;
    const exam = parseInt(data.get('exam')?.toString() || '') || undefined;

    if (!studentId || !term || !yearKey || !academicYear || !subject) {
      return fail(400, { 
        studentId, term, yearKey, academicYear, subject,
        missing: true 
      });
    }

    if (!locals.user) {
      return fail(401, { notAuthenticated: true });
    }

    try {
      // Get student to verify school
      const student = await getById<SchoolUser>(studentId);
      if (!student) {
        return fail(404, { error: 'Student not found' });
      }

      // Verify user has permission (admin or teacher)
      // const userSchoolRoles = await searchByPayload<SchoolUser>({
      //   u: locals.user.id,
      //   sc: student.sc,
      //   s: 'sch_usr'
      // });
      
      // const userRole = userSchoolRoles[0];
      // const isAuthorized = userRole?.r.includes('admin') || userRole?.r.includes('teacher');
      
      // if (!isAuthorized) {
      //   return fail(403, { error: 'Not authorized' });
      // }

      // Create score
      const score: Score = {
        s: 'sch_scr',
        u: studentId,
        t: term,
        c: parseInt(yearKey),
        c: academicYear,
        j: subject
      };

      if (ca1) score['1'] = ca1;
      if (ca2) score['2'] = ca2;
      if (ca3) score['3'] = ca3;
      if (project) score.p = project;
      if (exam) score.e = exam;

      const savedScore = await upsertPoint(score);
      throw redirect(303, `/s/${savedScore.id}`);
    } catch (error) {
      console.error('Error submitting score:', error);
      return fail(500, { error: 'Failed to save score' });
    }
  }
} satisfies Actions;

<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  
  let selectedStudent: SchoolUser | null = null;
  let loading = true;
  
  onMount(async () => {
    // Check authentication
    if (!$page.data.session) {
      goto('/');
      return;
    }
    
    // Check if student was pre-selected
    const studentId = $page.url.searchParams.get('student');
    if (studentId) {
      selectedStudent = await getById<SchoolUser>(COLLECTIONS.SCHOOL_USERS, studentId);
      if (selectedStudent) {
        await loadSchoolAndAuth();
      }
    }
    
    loading = false;
    
    // Commented out animations
    /*
    const anime = ((await import('animejs')) as any).default || (await import('animejs'));
    
    // Animate form
    anime({
      targets: '.score-container',
      opacity: [0, 1],
      translateY: [30, 0],
      duration: 800,
      easing: 'easeOutCubic'
    });
    */
  });
</script>

{#if loading}
  <div class="flex justify-center items-center min-h-[50vh]">
    <div class="glassmorphic p-4 rounded-full">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
    </div>
  </div>
{:else}
  <div class="max-w-3xl mx-auto">
    <div class="score-container card-glass" style="opacity: 0">
      <h1 class="text-3xl font-bold text-primary mb-8 text-center">Add Score</h1>
      
      {#if !selectedStudent}
        <!-- Student Search -->
        <div class="mb-8">
          <h2 class="text-xl font-semibold text-gray-800 mb-4">Search for Student</h2>
          
          <form on:submit|preventDefault={searchStudents} class="space-y-4">
            <div class="relative">
              <input
                type="text"
                bind:value={searchQuery}
                placeholder="Enter student name"
                class="input-neumorphic w-full"
                disabled={searching}
              />
            </div>
            
            {#if searchResults.length > 0}
              <div class="space-y-2">
                {#each searchResults as student}
                  <button
                    type="button"
                    on:click={() => selectStudent(student)}
                    class="w-full text-left glassmorphic p-3 rounded-lg hover:scale-[1.02] transition-transform"
                  >
                    <p class="font-medium">{student.n}</p>
                  </button>
                {/each}
              </div>
            {/if}
            
            <button
              type="submit"
              class="btn-neumorphic w-full"
              disabled={searching || !searchQuery.trim()}
            >
              {searching ? 'Searching...' : 'Search'}
            </button>
          </form>
        </div>
      {:else if isAuthorized}
        <!-- Score Form -->
        <div class="glassmorphic p-4 rounded-lg mb-6">
          <p class="text-lg font-semibold text-gray-800">Student: {selectedStudent.n}</p>
          <p class="text-sm text-gray-600">School: {school?.n}</p>
        </div>
        
        <form on:submit|preventDefault={saveScore} class="space-y-6">
          <div class="grid md:grid-cols-3 gap-4">
            <!-- Term -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Term <span class="text-red-500">*</span>
              </label>
              <select
                bind:value={term}
                class="input-neumorphic w-full"
                disabled={saving}
              >
                <option value={1}>1st Term</option>
                <option value={2}>2nd Term</option>
                <option value={3}>3rd Term</option>
              </select>
            </div>
            
            <!-- Academic Year -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Academic Year <span class="text-red-500">*</span>
              </label>
              <select
                bind:value={yearKey}
                class="input-neumorphic w-full"
                disabled={saving}
              >
                <option value="">Select year</option>
                {#each Object.entries(school?.c || {}).filter(([k]) => k !== 'next') as [key, year]}
                  <option value={key}>{year}</option>
                {/each}
              </select>
            </div>
            
            <!-- Session -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Session Year <span class="text-red-500">*</span>
              </label>
              <input
                type="number"
                bind:value={academicYear}
                min="2000"
                max="2100"
                class="input-neumorphic w-full"
                disabled={saving}
              />
            </div>
          </div>
          
          <!-- Subject -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Subject <span class="text-red-500">*</span>
            </label>
            <select
              bind:value={subject}
              class="input-neumorphic w-full"
              disabled={saving}
            >
              <option value="">Select subject</option>
              {#each Object.entries(subjects) as [name, code]}
                <option value={code}>{name}</option>
              {/each}
            </select>
          </div>
          
          <!-- Scores -->
          <div class="space-y-4">
            <h3 class="text-lg font-semibold text-gray-800">Scores (Optional)</h3>
            
            <div class="grid md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  1st CA
                </label>
                <input
                  type="number"
                  bind:value={ca1}
                  min="0"
                  max="100"
                  placeholder="0-100"
                  class="input-neumorphic w-full"
                  disabled={saving}
                />
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  2nd CA
                </label>
                <input
                  type="number"
                  bind:value={ca2}
                  min="0"
                  max="100"
                  placeholder="0-100"
                  class="input-neumorphic w-full"
                  disabled={saving}
                />
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  3rd CA
                </label>
                <input
                  type="number"
                  bind:value={ca3}
                  min="0"
                  max="100"
                  placeholder="0-100"
                  class="input-neumorphic w-full"
                  disabled={saving}
                />
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Project
                </label>
                <input
                  type="number"
                  bind:value={project}
                  min="0"
                  max="100"
                  placeholder="0-100"
                  class="input-neumorphic w-full"
                  disabled={saving}
                />
              </div>
              
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Exam
                </label>
                <input
                  type="number"
                  bind:value={exam}
                  min="0"
                  max="100"
                  placeholder="0-100"
                  class="input-neumorphic w-full"
                  disabled={saving}
                />
              </div>
            </div>
          </div>
          
          {#if error}
            <div class="text-red-500 text-sm glassmorphic p-3 rounded-lg">
              {error}
            </div>
          {/if}
          
          <div class="flex gap-4">
            <button
              type="submit"
              class="btn-neumorphic flex-1 disabled:opacity-50"
              disabled={saving}
            >
              {saving ? 'Saving...' : 'Save Score'}
            </button>
            
            <button
              type="button"
              on:click={() => selectedStudent = null}
              class="btn-neumorphic"
              disabled={saving}
            >
              Change Student
            </button>
          </div>
        </form>
      {/if}
    </div>
  </div>
{/if}